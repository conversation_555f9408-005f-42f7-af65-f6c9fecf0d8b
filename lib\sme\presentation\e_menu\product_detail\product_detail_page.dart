import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_images.dart';
import '../../../../core/theme/app_style.dart';
import '../../../../core/widgets/default_button.dart';
import '../widgets/product_widget.dart';
import 'product_detail_controller.dart';

class ProductDetailPage extends StatelessWidget {
  const ProductDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0);

    Map<String, dynamic> convertMap(dynamic originalMap) {
      if (originalMap == null) return {};

      if (originalMap is Map<String, dynamic>) {
        return originalMap;
      }

      if (originalMap is Map) {
        return originalMap.cast<String, dynamic>();
      }

      return {};
    }

    return GetBuilder<ProductDetailController>(
      init: ProductDetailController(Get.arguments),
      builder: (controller) {
        //debugPrint('Building ProductDetailPage');
        //debugPrint('Product: ${controller.product}');
        // debugPrint('Default items: ${controller.defaultItems}');
        // debugPrint('Choices: ${controller.choices}');
        // debugPrint('Selected choices: ${controller.selectedChoices}');
        final product = controller.product;
        final price = (product['Price'] as num?)?.toDouble() ?? 0.0;
        final itemName = product['ItemName'] ?? '';
        final description = product['ItemDesc'] ?? '';
        final images = product['Images'] as List? ?? [];
        final imageUrl = images.isNotEmpty ? images.first['MediaUrl'] ?? '' : '';

        return Scaffold(
          backgroundColor: AppColors.background,
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            behavior: HitTestBehavior.opaque,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            Image.network(
                              getValidImageUrl(imageUrl),
                              width: double.infinity,
                              height: width * 0.6,
                              fit: BoxFit.cover,
                              errorBuilder: (_, __, ___) => SizedBox(
                                width: double.infinity,
                                height: width * 0.6,
                                child: Image.asset('assets/images/general/no_image.jpg', fit: BoxFit.cover,)
                              ),
                            ),
                            Positioned(
                              top: MediaQuery.of(context).padding.top + 15,
                              left: width * 0.03,
                              child: InkWell(
                                onTap: () => Get.back(),
                                child: Container(
                                  padding: EdgeInsets.all(width * 0.01),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withOpacity(0.4),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(Icons.arrow_back, color: Colors.white),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: width * 0.03, vertical: width * 0.015),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Text(
                                      itemName,
                                      style: PrimaryFont.bold.copyWith(
                                        fontSize: width * 0.04,
                                        color: AppColors.text,
                                      ),
                                      softWrap: true,
                                      overflow: TextOverflow.visible,
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () => _showPriceEditDialog(context, controller, width),
                                    child: Container(
                                      padding: EdgeInsets.all(width * 0.01),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey),
                                        borderRadius: BorderRadius.circular(width * 0.01),
                                      ),
                                      child: Obx(() => Text(
                                        currencyFormat.format(controller.tempPrice.value),
                                        style: PrimaryFont.bold.copyWith(
                                          fontSize: width * 0.04,
                                          color: AppColors.primary,
                                        ),
                                      )),
                                    ),
                                  ),
                                ],
                              ),
                              if (description.isNotEmpty) ...[
                                Text(
                                  description,
                                  style: PrimaryFont.regular.copyWith(
                                    fontSize: width * 0.035,
                                    color: AppColors.text.withOpacity(0.7),
                                  ),
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ],
                          ),
                        ),
                        if (controller.defaultItems.isNotEmpty)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: width * 0.03),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  itemName,
                                  style: PrimaryFont.bold.copyWith(
                                    fontSize: width * 0.03,
                                    color: AppColors.text,
                                  ),
                                ),
                                SizedBox(height: width * 0.01),
                                ...controller.defaultItems.map((item) => Padding(
                                  padding: EdgeInsets.symmetric(vertical: width * 0.005),
                                  child: Row(
                                    children: [
                                      Icon(
                                          Icons.check_circle_outline,
                                          size: width * 0.04,
                                          color: AppColors.primary
                                      ),
                                      SizedBox(width: width * 0.02),
                                      Text(
                                        '${item['ItemName']} x${item['Qty']}',
                                        style: PrimaryFont.regular.copyWith(
                                          fontSize: width * 0.03,
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                              ],
                            ),
                          ),
                        if (controller.choices.isNotEmpty)
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: width * 0.03),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ...controller.choices.map((choice) {
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      SizedBox(height: width * 0.03),
                                      Text(
                                        '${choice['ChoiceName']} (Chọn tối đa ${choice['MaxChoice']})',
                                        style: PrimaryFont.bold.copyWith(
                                          fontSize: width * 0.03,
                                          color: AppColors.text,
                                        ),
                                      ),
                                      SizedBox(height: width * 0.01),
                                      ...(choice['Details'] as List<dynamic>).map<Widget>((dynamic detail) {
                                        final item = convertMap(detail);
                                        final isSelected = (controller.selectedChoices[choice['ChoiceId'].toString()] ?? [])
                                            .any((selected) => selected['ItemId'] == item['ItemId']);

                                        return GestureDetector(
                                          onTap: () {
                                            if (isSelected ||
                                                (controller.selectedChoices[choice['ChoiceId'].toString()]?.length ?? 0) <
                                                    (choice['MaxChoice'] as int)) {
                                              controller.toggleChoiceItem(
                                                choice['ChoiceId'].toString(),
                                                item,
                                              );
                                            }
                                          },
                                          child: Container(
                                            margin: EdgeInsets.only(bottom: width * 0.01),
                                            padding: EdgeInsets.all(width * 0.02),
                                            decoration: BoxDecoration(
                                              color: isSelected ? AppColors.primary.withOpacity(0.1) : Colors.transparent,
                                              border: Border.all(
                                                color: isSelected ? AppColors.primary : Colors.grey[300]!,
                                              ),
                                              borderRadius: BorderRadius.circular(width * 0.01),
                                            ),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  isSelected ? Icons.check_box : Icons.check_box_outline_blank,
                                                  color: isSelected ? AppColors.primary : Colors.grey,
                                                  size: width * 0.05,
                                                ),
                                                SizedBox(width: width * 0.02),
                                                Expanded(
                                                  child: Text(
                                                    item['ItemName'],
                                                    style: PrimaryFont.regular.copyWith(
                                                      fontSize: width * 0.03,
                                                    ),
                                                  ),
                                                ),
                                                if ((item['Price'] as num).toDouble() > 0)
                                                  Text(
                                                    '+${currencyFormat.format((item['Price'] as num).toDouble())}',
                                                    style: PrimaryFont.regular.copyWith(
                                                      fontSize: width * 0.03,
                                                      color: AppColors.primary,
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ),
                                        );
                                      }),
                                    ],
                                  );
                                }),
                              ],
                            ),
                          ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: width * 0.03),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'note'.tr,
                                style: PrimaryFont.bold.copyWith(
                                  fontSize: width * 0.03,
                                  color: AppColors.text,
                                ),
                              ),
                              SizedBox(height: width * 0.01),
                              GestureDetector(
                                onTap: () {
                                  _showNoteDialog(context, controller, width);
                                },
                                child: Container(
                                  height: width * 0.25,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(width * 0.02),
                                  ),
                                  padding: EdgeInsets.all(width * 0.02),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Obx(() => Text(
                                          controller.note.value.isEmpty ? 'note_hint'.tr : controller.note.value,
                                          style: PrimaryFont.regular.copyWith(
                                            fontSize: width * 0.03,
                                            color: controller.note.value.isEmpty ? AppColors.shadow : AppColors.text,
                                          ),
                                        )),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            color: AppColors.background,
            padding: EdgeInsets.only(
              left: width * 0.03,
              right: width * 0.03,
              bottom: width * 0.03,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: width * 0.1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.remove_circle,
                          size: width * 0.06,
                          color: AppColors.button,
                        ),
                        onPressed: controller.decreaseQuantity,
                      ),
                      GestureDetector(
                        onTap: () => _showQuantityDialog(context, controller, width),
                        child: Obx(() => Container(
                          padding: EdgeInsets.symmetric(horizontal: width * 0.035),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.button),
                            borderRadius: BorderRadius.circular(width * 0.01),
                          ),
                          child: Text(
                            '${controller.selectedQuantity.value}',
                            style: PrimaryFont.bold.copyWith(
                              fontSize: width * 0.05,
                              color: AppColors.text,
                            ),
                          ),
                        )),
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.add_circle,
                          size: width * 0.06,
                          color: AppColors.button,
                        ),
                        onPressed: controller.increaseQuantity,
                      ),
                    ],
                  ),
                ),
                Obx(() {
                  final isEdit = controller.isEditMode;
                  final qty = controller.selectedQuantity.value;

                  if (isEdit && qty == 0) {
                    return DefaultButton(
                      onPress: () {
                        if (controller.cartIndex >= 0) {
                          controller.cartController.removeFromCart(controller.cartIndex);
                          Get.back();
                        }
                      },
                      title: 'delete'.tr,
                      titleStyle: PrimaryFont.bold.copyWith(
                        fontSize: width * 0.04,
                        color: AppColors.background,
                      ),
                      color: AppColors.danger,
                      widthPercentage: 1,
                      borderRadius: width * 0.02,
                      heightPercentage: 0.12,
                    );
                  }

                  return DefaultButton(
                    onPress: controller.addToCart,
                    color: AppColors.button,
                    widthPercentage: 1,
                    borderRadius: width * 0.02,
                    heightPercentage: 0.12,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: width * 0.05),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            isEdit ? 'update_cart'.tr : 'add_to_cart'.tr,
                            style: PrimaryFont.bold.copyWith(
                              fontSize: width * 0.04,
                              color: AppColors.background,
                            ),
                          ),
                          Obx(() {
                            double extraPrice = 0.0;

                            controller.selectedChoices.forEach((_, items) {
                              for (var item in items) {
                                final extra = (item['Price'] as num?)?.toDouble() ?? 0.0;
                                extraPrice += extra;
                              }
                            });

                            final total = (controller.tempPrice.value + extraPrice) * qty;

                            return Text(
                              currencyFormat.format(total),
                              style: PrimaryFont.bold.copyWith(
                                fontSize: width * 0.04,
                                color: AppColors.background,
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  );
                })

              ],
            ),
          ),
        );
      },
    );
  }
}

void _showNoteDialog(BuildContext context, ProductDetailController controller, double width) {
  final textController = TextEditingController(text: controller.note.value);

  Get.dialog(
    Dialog(
      backgroundColor: AppColors.background,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(width * 0.02),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: width * 0.03),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: width * 0.02),
                  child: Text(
                    'note'.tr,
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close,
                      color: AppColors.text,
                      size: width * 0.05),
                  onPressed: () => Get.back(),
                ),
              ],
            ),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.02),
              child: TextField(
                controller: textController,
                decoration: InputDecoration(
                  hintText: 'note_hint'.tr,
                  hintStyle: PrimaryFont.regular.copyWith(
                    color: AppColors.shadow,
                    fontSize: width * 0.03,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.01),
                  ),
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                      horizontal: width * 0.03,
                      vertical: width * 0.03),
                ),
                maxLines: 3,
                maxLength: 100,
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
            ),

            Padding(
              padding: EdgeInsets.only(
                bottom: width * 0.03,
                right: width * 0.02,
                left: width * 0.02,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'cancel'.tr,
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                  SizedBox(width: width * 0.02),
                  ElevatedButton(
                    onPressed: () {
                      controller.note.value = textController.text.trim();
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      padding: EdgeInsets.symmetric(horizontal: width * 0.02, vertical: width * 0.015),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'confirm'.tr,
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

void _showQuantityDialog(BuildContext context, ProductDetailController controller, double width) {
  final textController = TextEditingController(
    text: controller.selectedQuantity.value.toString(),
  );

  Get.dialog(
    Dialog(
      backgroundColor: AppColors.background,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(width * 0.02),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: width * 0.03),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: width * 0.02),
                  child: Text(
                    'Nhập số lượng',
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: AppColors.text, size: width * 0.05),
                  onPressed: () => Get.back(),
                ),
              ],
            ),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.02),
              child: TextField(
                controller: textController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Số lượng',
                  labelStyle: PrimaryFont.regular.copyWith(
                    color: AppColors.shadow,
                    fontSize: width * 0.03,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.01),
                  ),
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: width * 0.03,
                    vertical: width * 0.03,
                  ),
                ),
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
            ),

            Padding(
              padding: EdgeInsets.only(
                bottom: width * 0.03,
                right: width * 0.02,
                left: width * 0.02,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'Hủy',
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                  SizedBox(width: width * 0.02),
                  ElevatedButton(
                    onPressed: () {
                      final quantity = double.tryParse(textController.text) ?? 1;
                      if (quantity > 0) {
                        controller.selectedQuantity.value = quantity;
                      }
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      padding: EdgeInsets.symmetric(horizontal: width * 0.02, vertical: width * 0.015),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'Xác nhận',
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

void _showPriceEditDialog(BuildContext context, ProductDetailController controller, double width) {
  final priceController = TextEditingController(
    text: controller.tempPrice.value.toStringAsFixed(0),
  );

  Get.dialog(
    Dialog(
      backgroundColor: AppColors.background,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(width * 0.02),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: width * 0.03),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: width * 0.02),
                  child: Text(
                    'Nhập giá tạm thời',
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.text,
                      fontSize: width * 0.04,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: AppColors.text, size: width * 0.05),
                  onPressed: () => Get.back(),
                ),
              ],
            ),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.02),
              child: TextField(
                controller: priceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Giá',
                  labelStyle: PrimaryFont.regular.copyWith(
                    color: AppColors.shadow,
                    fontSize: width * 0.03,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(width * 0.01),
                  ),
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: width * 0.03,
                    vertical: width * 0.03,
                  ),
                ),
                style: PrimaryFont.regular.copyWith(
                  color: AppColors.text,
                  fontSize: width * 0.03,
                ),
              ),
            ),

            Padding(
              padding: EdgeInsets.only(
                bottom: width * 0.03,
                right: width * 0.02,
                left: width * 0.02,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'Hủy',
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                  SizedBox(width: width * 0.02),
                  ElevatedButton(
                    onPressed: () {
                      final newPrice = double.tryParse(priceController.text) ?? controller.originalPrice.value;
                      controller.updateTempPrice(newPrice);
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      minimumSize: Size(width * 0.2, width * 0.08),
                      padding: EdgeInsets.symmetric(horizontal: width * 0.02, vertical: width * 0.015),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(width * 0.02),
                      ),
                    ),
                    child: Text(
                      'Lưu',
                      style: PrimaryFont.bold.copyWith(
                        color: AppColors.background,
                        fontSize: width * 0.03,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
