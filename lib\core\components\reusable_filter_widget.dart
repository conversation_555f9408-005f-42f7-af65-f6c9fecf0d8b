import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class FilterData {
  DateTime dateFrom;
  DateTime dateTo;
  String statusSelected;
  String statusName;
  String paymentMethodSelected;
  String paymentMethodName;
  String viewType;

  FilterData({
    required this.dateFrom,
    required this.dateTo,
    required this.statusSelected,
    required this.statusName,
    required this.paymentMethodSelected,
    required this.paymentMethodName,
    required this.viewType,
  });

  FilterData copyWith({
    DateTime? dateFrom,
    DateTime? dateTo,
    String? statusSelected,
    String? statusName,
    String? paymentMethodSelected,
    String? paymentMethodName,
    String? viewType,
  }) {
    return FilterData(
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
      statusSelected: statusSelected ?? this.statusSelected,
      statusName: statusName ?? this.statusName,
      paymentMethodSelected:
          paymentMethodSelected ?? this.paymentMethodSelected,
      paymentMethodName: paymentMethodName ?? this.paymentMethodName,
      viewType: viewType ?? this.viewType,
    );
  }
}

class ReusableFilterWidget extends StatefulWidget {
  final FilterData filterData;
  final Map<String, String> statuses;
  final List paymentMethods;
  final Function(FilterData) onFilterChanged;
  final VoidCallback onClearFilter;
  final bool showOnlyDateFilter; // Thêm parameter để chỉ hiển thị filter ngày

  const ReusableFilterWidget({
    super.key,
    required this.filterData,
    required this.statuses,
    required this.paymentMethods,
    required this.onFilterChanged,
    required this.onClearFilter,
    this.showOnlyDateFilter = false, // Mặc định là false để giữ nguyên behavior cũ
  });

  @override
  State<ReusableFilterWidget> createState() => _ReusableFilterWidgetState();

  // Static method để hiển thị date filter modal từ bên ngoài
  static void showDateFilterModal(
    BuildContext context,
    FilterData filterData,
    Function(FilterData) onFilterChanged,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _DateFilterModal(
        filterData: filterData,
        onFilterChanged: onFilterChanged,
      ),
    );
  }
}

class _ReusableFilterWidgetState extends State<ReusableFilterWidget> {

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: widget.showOnlyDateFilter
            ? _buildDateOnlyFilter(context)
            : SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // Date filter chip
                    _buildFilterChip(
                      context: context,
                      label: _getDateLabel(),
                      onTap: () => _showDateFilterModal(context),
                    ),
                    const SizedBox(width: 12),

                    // View type filter chip (Đơn hàng/Khách hàng)
                    _buildFilterChip(
                      context: context,
                      label: widget.filterData.viewType == 'order'
                          ? 'Đơn hàng'
                          : 'Khách hàng',
                      onTap: () => _toggleViewTypeDropdown(),
                    ),
                    const SizedBox(width: 12),

                    // Payment method filter chip (only for orders)
                    if (widget.filterData.viewType == 'order') ...[
                      _buildFilterChip(
                        context: context,
                        label: widget.filterData.paymentMethodName.isEmpty
                            ? 'Phân loại'
                            : widget.filterData.paymentMethodName,
                        onTap: () => _togglePaymentMethodDropdown(),
                      ),
                      const SizedBox(width: 12),

                      // Payment status filter chip
                      _buildFilterChip(
                        context: context,
                        label: widget.filterData.statusName.isEmpty
                            ? 'Trạng thái'
                            : widget.filterData.statusName,
                        onTap: () => _togglePaymentStatusDropdown(),
                      ),
                    ],
                  ],
                ),
              ),
        ),
      ],
    );
  }

  void _toggleViewTypeDropdown() {
    _showViewTypeFilterModal(context);
  }

  void _togglePaymentMethodDropdown() {
    _showPaymentMethodFilterModal(context);
  }

  void _togglePaymentStatusDropdown() {
    _showPaymentStatusFilterModal(context);
  }

  void _showViewTypeFilterModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.27,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: _ViewTypeFilterModal(
          filterData: widget.filterData,
          onFilterChanged: widget.onFilterChanged,
        ),
      ),
    );
  }

  void _showPaymentMethodFilterModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.4,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: _PaymentMethodFilterModal(
          filterData: widget.filterData,
          paymentMethods: widget.paymentMethods,
          onFilterChanged: widget.onFilterChanged,
        ),
      ),
    );
  }

  void _showPaymentStatusFilterModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.55,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: _PaymentStatusFilterModal(
          filterData: widget.filterData,
          statuses: widget.statuses,
          onFilterChanged: widget.onFilterChanged,
        ),
      ),
    );
  }



  Widget _buildFilterChip({
    required BuildContext context,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomText(
              text: label,
              size: 14,
              color: Colors.grey.shade700,
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  String _getDateLabel() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final filterFromDate = DateTime(widget.filterData.dateFrom.year,
        widget.filterData.dateFrom.month, widget.filterData.dateFrom.day);
    final filterToDate = DateTime(widget.filterData.dateTo.year,
        widget.filterData.dateTo.month, widget.filterData.dateTo.day);

    if (filterFromDate == today && filterToDate == today) {
      return 'Hôm nay';
    } else if (filterFromDate == today.subtract(const Duration(days: 1)) &&
        filterToDate == today.subtract(const Duration(days: 1))) {
      return 'Hôm qua';
    } else if (filterFromDate.year == now.year &&
        filterFromDate.month == now.month &&
        filterFromDate.day == 1 &&
        filterToDate.year == now.year &&
        filterToDate.month == now.month) {
      return 'Tháng này';
    } else if (filterFromDate.year == now.year &&
        filterFromDate.month == now.month - 1 &&
        filterFromDate.day == 1) {
      return 'Tháng trước';
    } else if (filterFromDate.year == now.year &&
        filterFromDate.month == 1 &&
        filterFromDate.day == 1 &&
        filterToDate.year == now.year &&
        filterToDate.month == 12 &&
        filterToDate.day == 31) {
      return 'Năm này';
    } else {
      return '${DateFormat('dd/MM').format(widget.filterData.dateFrom)} - ${DateFormat('dd/MM').format(widget.filterData.dateTo)}';
    }
  }

  Widget _buildDateOnlyFilter(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.calendar_today,
          color: AppColors.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: InkWell(
            onTap: () => _showDateFilterModal(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: _getDateLabel(),
                    size: 16,
                    color: AppColors.primary,
                    bold: true,
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showDateFilterModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _DateFilterModal(
        filterData: widget.filterData,
        onFilterChanged: widget.onFilterChanged,
      ),
    );
  }
}

// Date Filter Modal
class _DateFilterModal extends StatefulWidget {
  final FilterData filterData;
  final Function(FilterData) onFilterChanged;

  const _DateFilterModal({
    required this.filterData,
    required this.onFilterChanged,
  });

  @override
  State<_DateFilterModal> createState() => _DateFilterModalState();
}

class _DateFilterModalState extends State<_DateFilterModal> {
  bool _showFromDatePicker = false;
  bool _showToDatePicker = false;
  late FilterData _tempFilterData; // Temporary data cho preview
  late FilterData _originalFilterData; // Backup original data
  bool _hasChanges = false; // Track nếu có thay đổi

  @override
  void initState() {
    super.initState();
    _tempFilterData = widget.filterData;
    _originalFilterData = widget.filterData;
  }

  @override
  void didUpdateWidget(_DateFilterModal oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Khi filterData thay đổi từ bên ngoài, cập nhật temp state
    if (oldWidget.filterData != widget.filterData) {
      setState(() {
        _tempFilterData = widget.filterData;
        _originalFilterData = widget.filterData;
      });
    }
  }

  void _updateTempFilterData(FilterData newFilterData) {
    setState(() {
      _tempFilterData = newFilterData; // Chỉ cập nhật temp, chưa apply
      // Check nếu có thay đổi so với original
      _hasChanges = _tempFilterData.dateFrom != _originalFilterData.dateFrom ||
                   _tempFilterData.dateTo != _originalFilterData.dateTo;
    });
  }

  void _applyFilter() {
    widget.onFilterChanged(_tempFilterData); // Apply temp data
    Navigator.pop(context);
  }

  void _toggleFromDatePicker() {
    setState(() {
      _showFromDatePicker = !_showFromDatePicker;
      _showToDatePicker = false; // Close other picker
    });
  }

  void _toggleToDatePicker() {
    setState(() {
      _showToDatePicker = !_showToDatePicker;
      _showFromDatePicker = false; // Close other picker
    });
  }

  @override
  Widget build(BuildContext context) {
    // Tính chiều cao dựa trên việc có hiển thị date picker hay không
    double height = MediaQuery.of(context).size.height * 0.63;
    if (_showFromDatePicker || _showToDatePicker) {
      height = MediaQuery.of(context).size.height * 0.67; // Giảm xuống 80% vì calendar nhỏ hơn
    }

    return Container(
      height: height,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Title with close button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomText(
                text: 'Lọc theo thời gian',
                size: 18,
                bold: true,
                textAlign: TextAlign.left,

              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(Icons.close, color: Colors.grey.shade600),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick date options in grid (ẩn khi hiển thị calendar)
          if (!_showFromDatePicker && !_showToDatePicker) ...[
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              children: [
                _buildQuickDateChip('Hôm nay', () => _setQuickDate(context, 0)),
                _buildQuickDateChip('Hôm qua', () => _setQuickDate(context, 1)),
                _buildQuickDateChip('Tháng này', () => _setCurrentMonth(context),
                    isSelected: _isCurrentMonth()),
                _buildQuickDateChip(
                    'Tháng trước', () => _setPreviousMonth(context)),
                _buildQuickDateChip('30 ngày', () => _setQuickDate(context, 30)),
                _buildQuickDateChip('Năm này', () => _setCurrentYear(context)),
                // _buildQuickDateChip('Tất cả', () => _setAllTime(context)),
              ],
            ),
            const SizedBox(height: 16),
          ],

          // Custom date range section (ẩn khi hiển thị calendar)
          if (!_showFromDatePicker && !_showToDatePicker) ...[
            CustomText(
              text: 'Hoặc tùy chỉnh thời gian',
              size: 14,
              color: Colors.grey.shade600,
              bold: true,
            ),
            const SizedBox(height: 8),
          ],

          // Date range dropdowns (ẩn khi hiển thị calendar)
          if (!_showFromDatePicker && !_showToDatePicker) ...[
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                          text: 'Từ', size: 12, color: Colors.grey.shade600),
                      const SizedBox(height: 4),
                      _buildDateDropdown(
                        context: context,
                        date: _tempFilterData.dateFrom,
                        onTap: () => _toggleFromDatePicker(),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                          text: 'Đến', size: 12, color: Colors.grey.shade600),
                      const SizedBox(height: 4),
                      _buildDateDropdown(
                        context: context,
                        date: _tempFilterData.dateTo,
                        onTap: () => _toggleToDatePicker(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],

          // Inline date pickers
          if (_showFromDatePicker) ...[
            CustomText(
              text: 'Chọn ngày bắt đầu',
              size: 16,
              bold: true,
              color: AppColors.primary,
            ),
            const SizedBox(height: 8),
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  TableCalendar<DateTime>(
                    firstDay: DateTime(2020),
                    lastDay: DateTime(2030),
                    focusedDay: _tempFilterData.dateFrom,
                    selectedDayPredicate: (day) {
                      return isSameDay(_tempFilterData.dateFrom, day);
                    },
                    onDaySelected: (selectedDay, focusedDay) {
                      // Cập nhật temp data và đóng calendar
                      _updateTempFilterData(_tempFilterData.copyWith(dateFrom: selectedDay));
                      setState(() {
                        _showFromDatePicker = false;
                      });
                    },
                    calendarStyle: CalendarStyle(
                      // Styling cho ngày được chọn
                      selectedDecoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                      selectedTextStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      // Styling cho ngày hôm nay
                      todayDecoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                      ),
                      todayTextStyle: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                      // Styling chung
                      outsideDaysVisible: false,
                      weekendTextStyle: const TextStyle(color: Colors.red),
                    ),
                    headerStyle: HeaderStyle(
                      formatButtonVisible: false,
                      titleCentered: true,
                      titleTextStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      leftChevronIcon: Icon(
                        Icons.chevron_left,
                        color: AppColors.primary,
                      ),
                      rightChevronIcon: Icon(
                        Icons.chevron_right,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          if (_showToDatePicker) ...[
            CustomText(
              text: 'Chọn ngày kết thúc',
              size: 16,
              bold: true,
              color: AppColors.primary,
            ),
            const SizedBox(height: 8),
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TableCalendar<DateTime>(
                firstDay: DateTime(2020),
                lastDay: DateTime(2030),
                focusedDay: _tempFilterData.dateTo,
                selectedDayPredicate: (day) {
                  return isSameDay(_tempFilterData.dateTo, day);
                },
                onDaySelected: (selectedDay, focusedDay) {
                  // Cập nhật temp data và đóng calendar
                  _updateTempFilterData(_tempFilterData.copyWith(dateTo: selectedDay));
                  setState(() {
                    _showToDatePicker = false;
                  });
                },
                calendarStyle: CalendarStyle(
                  // Styling cho ngày được chọn
                  selectedDecoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                  selectedTextStyle: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  // Styling cho ngày hôm nay
                  todayDecoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  todayTextStyle: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                  // Styling chung
                  outsideDaysVisible: false,
                  weekendTextStyle: const TextStyle(color: Colors.red),
                ),
                headerStyle: HeaderStyle(
                  formatButtonVisible: false,
                  titleCentered: true,
                  titleTextStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  leftChevronIcon: Icon(
                    Icons.chevron_left,
                    color: AppColors.primary,
                  ),
                  rightChevronIcon: Icon(
                    Icons.chevron_right,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Action buttons (chỉ hiển thị khi có thay đổi và KHÔNG mở calendar)
          if (_hasChanges && !_showFromDatePicker && !_showToDatePicker)
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      // Reset về original data và đóng calendar
                      setState(() {
                        _tempFilterData = _originalFilterData;
                        _showFromDatePicker = false;
                        _showToDatePicker = false;
                        _hasChanges = false;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                    child: const CustomText(text: 'Hủy bỏ'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilter,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const CustomText(text: 'Áp dụng', color: Colors.white),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildQuickDateChip(String label, VoidCallback onTap,
      {bool isSelected = false}) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
          ),
        ),
        child: Center(
          child: CustomText(
            text: label,
            size: 14,
            color: isSelected ? Colors.white : Colors.black,
            bold: isSelected,
          ),
        ),
      ),
    );
  }

  Widget _buildDateDropdown({
    required BuildContext context,
    required DateTime date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(
              text: DateFormat('dd/MM/yyyy').format(date),
              size: 14,
            ),
            Icon(Icons.keyboard_arrow_down,
                color: Colors.grey.shade600, size: 20),
          ],
        ),
      ),
    );
  }

  bool _isCurrentMonth() {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, now.month, 1);
    final lastDay = DateTime(now.year, now.month + 1, 0);

    return _tempFilterData.dateFrom.year == firstDay.year &&
        _tempFilterData.dateFrom.month == firstDay.month &&
        _tempFilterData.dateFrom.day == firstDay.day &&
        _tempFilterData.dateTo.year == lastDay.year &&
        _tempFilterData.dateTo.month == lastDay.month &&
        _tempFilterData.dateTo.day == lastDay.day;
  }

  void _setQuickDate(BuildContext context, int daysAgo) {
    final now = DateTime.now();
    final targetDate = now.subtract(Duration(days: daysAgo));

    _updateTempFilterData(_tempFilterData.copyWith(
      dateFrom: targetDate,
      dateTo: now,
    ));
    // Apply ngay lập tức cho quick date
    _applyFilter();
  }

  void _setCurrentMonth(BuildContext context) {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, now.month, 1);
    final lastDay = DateTime(now.year, now.month + 1, 0);

    _updateTempFilterData(_tempFilterData.copyWith(
      dateFrom: firstDay,
      dateTo: lastDay,
    ));
    // Apply ngay lập tức cho quick date
    _applyFilter();
  }

  void _setPreviousMonth(BuildContext context) {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, now.month - 1, 1);
    final lastDay = DateTime(now.year, now.month, 0);

    _updateTempFilterData(_tempFilterData.copyWith(
      dateFrom: firstDay,
      dateTo: lastDay,
    ));
    // Apply ngay lập tức cho quick date
    _applyFilter();
  }

  void _setCurrentYear(BuildContext context) {
    final now = DateTime.now();
    final firstDay = DateTime(now.year, 1, 1);
    final lastDay = DateTime(now.year, 12, 31);

    _updateTempFilterData(_tempFilterData.copyWith(
      dateFrom: firstDay,
      dateTo: lastDay,
    ));
    // Apply ngay lập tức cho quick date
    _applyFilter();
  }

  // void _setAllTime(BuildContext context) {
//   //   final now = DateTime.now();
//   //   final firstDay = DateTime(2020, 1, 1);
//   //
//   //   _updateTempFilterData(_tempFilterData.copyWith(
//   //     dateFrom: firstDay,
//   //     dateTo: now,
//   //   ));
//   //   // Apply ngay lập tức cho quick date
//   //   _applyFilter();
//   // }


}

// View Type Filter Modal (Đơn hàng/Khách hàng)
class _ViewTypeFilterModal extends StatelessWidget {
  final FilterData filterData;
  final Function(FilterData) onFilterChanged;

  const _ViewTypeFilterModal({
    required this.filterData,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),


          // Title
          Row(
            children: [
              Icon(Icons.close, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              CustomText(
                text: 'Chọn loại',
                size: 18,
                bold: true,
              ),
            ],
          ),
          const SizedBox(height: 20),
          // View type options
          _buildViewTypeOption('Đơn hàng', 'order'),
          _buildViewTypeOption('Khách hàng', 'customer'),


        ],
      ),
    );
  }

  Widget _buildViewTypeOption(String label, String viewType) {
    final isSelected = filterData.viewType == viewType;
    return Builder(
        builder: (context) => InkWell(
              onTap: () {
                onFilterChanged(filterData.copyWith(viewType: viewType));
                Navigator.pop(context);
              },
              child: Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        isSelected ? AppColors.primary : Colors.grey.shade200,
                  ),
                ),
                child: CustomText(
                  text: label,
                  color: isSelected ? AppColors.primary : Colors.black,
                  bold: isSelected,
                ),
              ),
            ));
  }
}

// Payment Method Filter Modal
class _PaymentMethodFilterModal extends StatelessWidget {
  final FilterData filterData;
  final List paymentMethods;
  final Function(FilterData) onFilterChanged;

  const _PaymentMethodFilterModal({
    required this.filterData,
    required this.paymentMethods,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Row(
            children: [
              Icon(Icons.close, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              CustomText(
                text: 'Phương thức thanh toán',
                size: 18,
                bold: true,
              ),
            ],
          ),
          const SizedBox(height: 20),

          // All option
          InkWell(
            onTap: () {
              onFilterChanged(filterData.copyWith(
                paymentMethodSelected: '',
                paymentMethodName: 'Tất cả',
              ));
              Navigator.pop(context);
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: filterData.paymentMethodSelected.isEmpty
                    ? AppColors.primary.withValues(alpha: 0.1)
                    : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: filterData.paymentMethodSelected.isEmpty
                      ? AppColors.primary
                      : Colors.grey.shade200,
                ),
              ),
              child: CustomText(
                text: 'Tất cả',
                color: filterData.paymentMethodSelected.isEmpty
                    ? AppColors.primary
                    : Colors.black,
                bold: filterData.paymentMethodSelected.isEmpty,
              ),
            ),
          ),

          // Payment method options
          ...paymentMethods.map((method) {
            final isSelected =
                method['PayCode'] == filterData.paymentMethodSelected;
            return InkWell(
              onTap: () {
                onFilterChanged(filterData.copyWith(
                  paymentMethodSelected: method['PayCode'],
                  paymentMethodName: method['PayName'],
                ));
                Navigator.pop(context);
              },
              child: Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        isSelected ? AppColors.primary : Colors.grey.shade200,
                  ),
                ),
                child: CustomText(
                  text: method['PayName'],
                  color: isSelected ? AppColors.primary : Colors.black,
                  bold: isSelected,
                ),
              ),
            );
          }),

          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

// Payment Status Filter Modal
class _PaymentStatusFilterModal extends StatelessWidget {
  final FilterData filterData;
  final Map<String, String> statuses;
  final Function(FilterData) onFilterChanged;

  const _PaymentStatusFilterModal({
    required this.filterData,
    required this.statuses,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Row(
            children: [
              Icon(Icons.close, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              CustomText(
                text: 'Trạng thái thanh toán',
                size: 18,
                bold: true,
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Status options
          _buildStatusOption('Tất cả', ''),
          _buildStatusOption('Thành công', 'SUCCESS'),
          _buildStatusOption('Đang thanh toán', 'PENDING'),
          _buildStatusOption('Đã hủy', 'CANCELLED'),
          _buildStatusOption('Chờ thanh toán', 'WAITING'),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildStatusOption(String label, String statusCode) {
    final isSelected = filterData.statusSelected == statusCode;
    return Builder(
        builder: (context) => InkWell(
              onTap: () {
                onFilterChanged(filterData.copyWith(
                  statusSelected: statusCode,
                  statusName: label,
                ));
                Navigator.pop(context);
              },
              child: Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        isSelected ? AppColors.primary : Colors.grey.shade200,
                  ),
                ),
                child: CustomText(
                  text: label,
                  color: isSelected ? AppColors.primary : Colors.black,
                  bold: isSelected,
                ),
              ),
            ));
  }
}
