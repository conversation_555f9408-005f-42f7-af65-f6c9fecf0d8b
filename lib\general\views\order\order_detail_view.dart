import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_hr.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/order_controller.dart';
import 'package:gls_self_order/general/views/order/create_bill_view.dart';
import 'package:gls_self_order/general/views/order/widgets/order_row_info.dart';
import 'package:logger/logger.dart';

class OrderDetailView extends StatefulWidget {
  final String code;
  const OrderDetailView({super.key, required this.code});

  @override
  State<OrderDetailView> createState() => _OrderDetailViewState();
}

class _OrderDetailViewState extends State<OrderDetailView> {
  //variable
  OrderController orderController = Get.find();
  dynamic order;
  List orderDetails = [];
  dynamic statuses = {};
  Color statusColor = Colors.black;
  String statusName = '';
  final logger = Logger();

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getDetail();
  }

  getDetail() async {
    AppFunction.showLoading();
    List res = await orderController.getDetail(widget.code);
    order = res[0];
    orderDetails = res[1];
    await getStatusList();
    statusColor = getStatusColor(order['StatusCode']);
    try {
      statusName = statuses[order['StatusCode']];
    } catch (ex) {}
    AppFunction.hideLoading();
    logger.d('Order: $order');
    setState(() {});
  }

  getStatusList() async {
    List statusList = await orderController.getStatusList();
    for (dynamic item in statusList) {
      statuses[item['StatusCode']] = item['StatusName'];
    }
  }

  Widget renderItem(item) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color.fromRGBO(89, 219, 255, 0.3),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: '${item['ItemQty']}x  ',
                bold: true,
              ),
              Expanded(
                  child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                    text: item['ItemName'],
                    bold: true,
                  ),
                  if (item['ItemNote'] != null)
                    CustomText(
                      text: item['ItemNote'],
                      size: 14,
                    )
                ],
              )),
              CustomText(
                  text: AppFunction.formatMoney(item['TotalAmount'] ?? 0))
            ],
          ),
          SizedBox(
            height: 5,
          ),
          CustomHr(),
          SizedBox(
            height: 5,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              OrderRowInfo(
                label: 'Đơn giá: ',
                value: AppFunction.formatMoney(item['ItemPriceVAT'] ?? '0'),
              ),
              OrderRowInfo(
                label: 'VAT: ',
                value: AppFunction.formatMoney(item['VATAmount'] ?? '0'),
              ),
              OrderRowInfo(
                label: 'Giảm giá:',
                value: AppFunction.formatMoney(item['DiscountAmount'] ?? '0'),
              ),
              OrderRowInfo(
                label: 'Thành tiền: ',
                value: AppFunction.formatMoney(item['SubTotal'] ?? '0'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget renderInvoiceInfo() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(text: 'Thông tin hoá đơn', bold: true),
        SizedBox(
          height: 10,
        ),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.shadow.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.05),
                  spreadRadius: 0,
                  blurRadius: 1,
                  offset: Offset(0, 3)),
            ],
          ),
          padding: EdgeInsets.all(10),
          margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CustomText(text: 'Mã hoá đơn: '),
                  CustomText(
                    text: order['InvoiceNumber'] ?? '',
                    bold: true,
                  )
                ],
              ),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: 'Khách hàng:'),
                        CustomText(
                          text: order['ICustomerName'] ?? '',
                          bold: true,
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: 'Số điện thoại:'),
                        CustomText(
                          text: order['ICustomerPhone'] ?? '',
                          bold: true,
                        )
                      ],
                    ),
                  )
                ],
              ),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: 'Địa chỉ:'),
                        CustomText(
                          text: order['ICustomerAddress'] ?? '',
                          bold: true,
                        )
                      ],
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: 'Công ty:'),
                        CustomText(
                          text: order['ICompanyName'] ?? '',
                          bold: true,
                        )
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: 'MST:'),
                        CustomText(
                          text: order['ITaxCode'] ?? '',
                          bold: true,
                        )
                      ],
                    ),
                  )
                ],
              ),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: 'Địa chỉ công ty:'),
                        CustomText(
                          text: order['ICompanyAddress'] ?? '',
                          bold: true,
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Chi tiết đơn hàng'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: order != null
            ? ListView(
                padding: EdgeInsets.all(10),
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.shadow.withValues(alpha: 0.08),
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.05),
                            spreadRadius: 0,
                            blurRadius: 1,
                            offset: Offset(0, 3)),
                      ],
                    ),
                    padding: EdgeInsets.all(10),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      separatorBuilder: (context, index) => SizedBox(
                        height: 10,
                      ),
                      itemCount: 13,
                      itemBuilder: (context, index){
                        return [
                            OrderRowInfo(
                          label: 'Mã đơn hàng:',
                          value: order['OrderCode'] ?? '',
                        ),
                        OrderRowInfo(
                          label: 'Chi nhánh: ',
                          value: order['BranchName'] ?? '',
                        ),
                        OrderRowInfo(
                          label: 'Tiền hàng: ',
                          value: AppFunction.formatMoney(
                              order['OrderSubTotalVAT'] ?? '0'),
                        ),
                        OrderRowInfo(
                          label: 'Ngày:  ',
                          value: AppFunction.formatDateWithTime(
                              order['CreatedAt'] ?? ''),
                        ),
                        OrderRowInfo(
                          label: 'Giảm giá:  ',
                          value: AppFunction.formatMoney(
                              order['OrderDiscountAmountView'] ?? '0'),
                        ),
                        OrderRowInfo(
                          label: 'Khách hàng: ',
                          value: order['CustomerName'] ?? '',
                        ),
                        OrderRowInfo(
                          label: 'VAT: ',
                          value: AppFunction.formatMoney(
                              order['OrderVATAmount'] ?? '0'),
                        ),
                        OrderRowInfo(
                          label: 'Số điện thoại: ',
                          value: order['CustomerPhone'] ?? '',
                        ),
                        OrderRowInfo(
                          label: 'Tổng tiền: ',
                          value: AppFunction.formatMoney(
                              order['OrderTotalView'] ?? '0'),
                        ),
                        OrderRowInfo(
                          label: 'Ghi chú: ',
                          value: order['OrderNote'] ?? '',
                        ),
                        OrderRowInfo(
                          label: 'Trạng thái đơn hàng: ',
                          value: statusName,
                          color: statusColor,
                        ),
                        OrderRowInfo(
                          label: 'PTTT: ',
                          value: AppFunction.getPaymentMethodName(
                              order['PaymentMethod']),
                        ),
                        if (order['PaymentMsg'] != null &&
                            order['PaymentMsg'] != '')
                          OrderRowInfo(
                            label: 'Thông tin thanh toán: ',
                            value: order['PaymentMsg'] ?? '',
                          ),
                        if (order['InvoiceId'] == null)
                          Column(
                            children: [
                              SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  SizedBox(
                                    width: 125,
                                    height: 40,
                                    child: CustomButton(
                                        text: 'Xuất hoá đơn',
                                        onTap: () async {
                                          if (order['OrderCode'] != null) {
                                            final result = await Get.to(() =>
                                                CreateBillView(
                                                  orderCode:
                                                      order['OrderCode'] ?? '',
                                                ));
                                            if (result != null) {
                                              getDetail();
                                            }
                                          }
                                        },
                                        color: AppColors.primary),
                                  )
                                ],
                              )
                            ],
                          )
                        ][index];
                      },
                    ),
                    
                  ),
                  if (order['InvoiceId'] != null) renderInvoiceInfo(),
                  CustomText(text: 'Chi tiết đơn hàng', bold: true),
                  SizedBox(
                    height: 10,
                  ),
                  for (dynamic item in orderDetails) renderItem(item)
                ],
              )
            : Container(),
      ),
    );
  }

  Color getStatusColor(String statusCode) {
    switch (statusCode) {
      case 'TEMP': // Lưu tạm
        return Colors.grey;
      case 'WATTING_PAYMENT': // Đang chờ thanh toán
        return Colors.orange;
      case 'PAID': // Đã thanh toán
        return Colors.blue;
      case 'PAID_ERROR': // Đã thanh toán (Lỗi)
        return Colors.red;
      case 'CANCEL': // Đã hủy
        return Colors.red;
      case 'TIMEOUT': // Hết thời gian thanh toán
        return Colors.red[700]!;
      case 'SUCCESS': // Thành công
        return Colors.green;
      case 'WAITING': // Đang thực hiện đơn
        return Colors.blue[300]!;
      case 'SYSTEM_ERROR': // Lỗi hệ thống
        return Colors.red[900]!;
      default:
        return Colors.grey;
    }
  }

  String getStatusText(String statusCode) {
    switch (statusCode) {
      case 'TEMP':
        return 'Lưu tạm';
      case 'WATTING_PAYMENT':
        return 'Đang chờ thanh toán';
      case 'PAID':
        return 'Đã thanh toán';
      case 'PAID_ERROR':
        return 'Đã thanh toán (Lỗi)';
      case 'CANCEL':
        return 'Đã hủy';
      case 'TIMEOUT':
        return 'Hết thời gian thanh toán';
      case 'SUCCESS':
        return 'Thành công';
      case 'WAITING':
        return 'Đang thực hiện đơn';
      case 'SYSTEM_ERROR':
        return 'Lỗi hệ thống';
      default:
        return statusCode;
    }
  }
}
